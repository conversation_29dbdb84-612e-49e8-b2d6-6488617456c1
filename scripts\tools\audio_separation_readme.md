# audio separation pipeline

comprehensive audio separation pipeline using demucs for separating audio into vocal and instrumental stems.

## features

- **multiple models**: support for various demucs models (htdemucs, htdemucs_ft, htdemucs_6s, etc.)
- **flexible stem extraction**: separate vocals, drums, bass, other, or all stems
- **multiple output formats**: wav, mp3, flac support
- **memory management**: configurable segment length for large files
- **batch processing**: process multiple files at once
- **production-ready**: comprehensive error handling, logging, and configuration
- **command-line interface**: easy-to-use cli with extensive options
- **programmatic api**: use as a python library in your projects

## installation

the pipeline requires the following dependencies (already included in the project):
- demucs >= 4.0.1
- torch, torchaudio
- pydub
- loguru

## usage

### command line interface

basic usage:
```bash
# separate all stems from an audio file
python scripts\tools\audio_separation.py input.mp3

# separate only vocals
python scripts\tools\audio_separation.py --stems vocals input.mp3

# use fine-tuned model for better quality (4x slower)
python scripts\tools\audio_separation.py --model htdemucs_ft input.mp3

# output as mp3 with custom bitrate
python scripts\tools\audio_separation.py --format mp3 --mp3-bitrate 256 input.wav

# process with memory management for large files
python scripts\tools\audio_separation.py --segment-length 30 input.mp3

# batch process multiple files
python scripts\tools\audio_separation.py *.mp3

# use cpu instead of gpu
python scripts\tools\audio_separation.py --device cpu input.mp3
```

### programmatic usage

```python
from scripts.tools.audio_separation import (
    AudioSeparationPipeline, 
    AudioSeparationConfig,
    SeparationModel,
    StemType,
    OutputFormat
)

# basic vocal separation
result = separate_vocals_only("input.mp3", "output/")

# custom configuration
config = AudioSeparationConfig(
    model=SeparationModel.HTDEMUCS_FT,
    stems=StemType.ALL,
    output_format=OutputFormat.MP3,
    mp3_bitrate=256,
    segment_length=30
)

pipeline = AudioSeparationPipeline(config)
result = pipeline.separate_audio("input.mp3")

print(f"output files: {result['output_files']}")
```

## available models

- `htdemucs`: default hybrid transformer model (recommended)
- `htdemucs_ft`: fine-tuned version (better quality, 4x slower)
- `htdemucs_6s`: 6-source model (adds piano and guitar)
- `hdemucs_mmi`: hybrid demucs v3 retrained
- `mdx`: trained on musdb hq only
- `mdx_extra`: trained with extra data
- `mdx_q`: quantized mdx (smaller, slightly worse quality)

## available stems

- `vocals`: vocal tracks
- `drums`: drum tracks  
- `bass`: bass tracks
- `other`: other instruments
- `piano`: piano (only with htdemucs_6s)
- `guitar`: guitar (only with htdemucs_6s)
- `all`: all available stems

## configuration options

the pipeline supports extensive configuration through command-line arguments or configuration files:

### performance options
- `--device`: cpu, cuda, or auto
- `--segment-length`: segment length for memory management
- `--jobs`: number of parallel jobs
- `--shifts`: prediction averaging (quality vs speed)
- `--overlap`: prediction window overlap

### quality options
- `--mp3-bitrate`: mp3 bitrate in kbps
- `--mp3-preset`: mp3 encoder preset (2=best, 7=fastest)
- `--float32`: save as float32 wav
- `--int24`: save as 24-bit wav
- `--clip-mode`: rescale or clamp

### configuration files
```bash
# save current configuration
python scripts\tools\audio_separation.py --save-config config.json input.mp3

# load configuration from file
python scripts\tools\audio_separation.py --config config.json input.mp3
```

## memory requirements

- minimum 3gb gpu memory for basic processing
- 7gb+ recommended for default settings
- use `--segment-length` to reduce memory usage
- cpu processing available with `--device cpu`

## output structure

separated files are saved in:
```
output_directory/
├── model_name/
│   └── track_name/
│       ├── vocals.wav
│       ├── drums.wav
│       ├── bass.wav
│       └── other.wav
```

## error handling

the pipeline includes comprehensive error handling:
- input file validation
- format compatibility checks
- memory requirement estimation
- graceful failure recovery
- detailed logging and debugging information

## logging

configurable logging levels:
- `--log-level DEBUG`: detailed debugging information
- `--log-level INFO`: standard processing information (default)
- `--log-level WARNING`: warnings and errors only
- `--log-level ERROR`: errors only
- `--quiet`: suppress non-error output
- `--verbose`: enable debug output

## examples

see the main function and example usage functions in the script for more detailed examples of how to use the pipeline both from command line and programmatically.
