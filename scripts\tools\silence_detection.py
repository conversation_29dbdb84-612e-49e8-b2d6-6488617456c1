"""silence_detector.py – robust silence‑/speech‑gap detection utility

features
--------
* works on **any audio/video** file that ffmpeg understands (mp3, wav, mp4 …).
* two detection engines:
    • **pydub** (default) – fast, pure‑python; good for ≤30‑min clips.
    • **ffmpeg** – streams, uses almost no ram; scales to hours‑long media.
* threshold can be **relative** to average loudness (e.g. 16 db below mean),
  or an **absolute** dbfs value.
* pretty timestamp formatting ``hh:mm:ss.mmm`` and json/csv export.
* sensible cli with progress logging.

usage examples
--------------
detect silences ≥800 ms that are 16 db below the average level:
```
python silence_detector.py input.mp3 --min-len 800 --rel-thresh 16
```
same thing, but let ffmpeg do the heavy lifting:
```
python silence_detector.py input.mp3 --method ffmpeg
```
generate a csv file you can load in excel:
```
python silence_detector.py input.wav -o gaps.csv --csv
```
"""

from __future__ import annotations

import argparse
import csv
import json
import re
import subprocess
import sys
import uuid
from dataclasses import dataclass
from enum import Enum, auto
from pathlib import Path
from typing import Any, Dict, Final, List, Optional, Union

from loguru import logger

# third‑party
from pydub import AudioSegment
from pydub.silence import detect_silence

# ---------------------------------------------------------------------------
# constants
# ---------------------------------------------------------------------------
_MILLIS: Final[int] = 1000
_TIMESTAMP_RE = re.compile(r"^(\d+):(\d+):(\d+)(?:[.,](\d{1,3}))?$")
_OUTPUT_DIR = "data/output"


# ---------------------------------------------------------------------------
# data structures
# ---------------------------------------------------------------------------
@dataclass(slots=True)
class Range:
    """represents a time range with start and end in seconds"""

    start: float  # seconds
    end: float  # seconds

    # ─────── representation helpers ───────────────────────────────────────
    def as_dict(self) -> dict[str, float]:
        return {"start": self.start, "end": self.end}

    def __str__(self) -> str:
        return f"{fmt(self.start)} – {fmt(self.end)}"


class DetectionMethod(Enum):
    """detection method options"""

    PYDUB = auto()
    FFMPEG = auto()

    @classmethod
    def from_string(cls, value: str) -> "DetectionMethod":
        """convert string value to enum"""
        mapping = {"pydub": cls.PYDUB, "ffmpeg": cls.FFMPEG}
        if value.lower() not in mapping:
            raise ValueError(f"Invalid detection method: {value}. Use 'pydub' or 'ffmpeg'")
        return mapping[value.lower()]


# ---------------------------------------------------------------------------
# utility functions
# ---------------------------------------------------------------------------
def setup_logger(level: str = "INFO"):
    """configure loguru with custom success level and pretty formatting"""
    # remove default handler
    logger.remove()

    # create custom success level if not already exists
    if "SUCCESS" not in logger._core.levels:
        logger.level("SUCCESS", no=25, color="<green>")

    # add stdout handler with nice format
    logger.add(
        sys.stdout,
        format=(
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<level>{message}</level>"
        ),
        colorize=True,
        level=level,
    )

    return logger


def fmt(seconds: float) -> str:
    """hh:mm:ss.mmm (always zero‑padded to 3‑digit ms)."""
    h, rem = divmod(seconds, 3600)
    m, s = divmod(rem, 60)
    ms = int(round((s - int(s)) * _MILLIS))
    return f"{int(h):02d}:{int(m):02d}:{int(s):02d}.{ms:03d}"


def parse_timestamp(ts: str) -> float:
    """inverse of :func:`fmt` – strict but forgiving on the ms separator."""
    m = _TIMESTAMP_RE.fullmatch(ts.strip())
    if not m:
        raise ValueError(f"Invalid timestamp '{ts}'. Use HH:MM:SS.mmm")
    h, m_, s, ms = m.groups()
    secs = int(h) * 3600 + int(m_) * 60 + int(s)
    if ms:
        secs += int(ms.ljust(3, "0")) / _MILLIS
    return secs


# initialize logger at module level
log = setup_logger()


class SilenceDetector:
    """professional silence detector for audio/video files

    provides multiple detection methods, configurable thresholds and output formats
    """

    def __init__(
        self,
        file_path: Union[str, Path],
        method: Union[str, DetectionMethod] = DetectionMethod.PYDUB,
        min_silence_len: int = 1000,
        rel_thresh: Optional[float] = 16.0,
        abs_thresh: Optional[float] = None,
        log_level: str = "INFO",
        downsample_rate: Optional[int] = None,
    ):
        """initialize the silence detector

        args:
            file_path: path to audio/video file
            method: detection method - 'pydub' or 'ffmpeg'
            min_silence_len: minimum silence length in milliseconds
            rel_thresh: db below average to count as silence
            abs_thresh: absolute dbfs threshold (negative value)
            log_level: logging level (INFO, DEBUG, etc.)
            downsample_rate: optional sample rate to downsample audio to (e.g. 16000)
                             for faster processing with pydub method
        """
        # validate file exists
        self.file_path = Path(file_path)
        if not self.file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # convert string method to enum if needed
        self.method = (
            method if isinstance(method, DetectionMethod) else DetectionMethod.from_string(method)
        )

        # validation
        if rel_thresh is not None and abs_thresh is not None:
            raise ValueError("Choose either rel_thresh or abs_thresh, not both")

        # store configuration
        self.min_silence_len = min_silence_len  # milliseconds
        self.rel_thresh = rel_thresh
        self.abs_thresh = abs_thresh
        self.downsample_rate = downsample_rate

        # prepare logger
        self.log = setup_logger(log_level)

        # cache for audio info and detection results
        self._audio_info: Optional[Dict[str, Any]] = None
        self._spans: Optional[List[Range]] = None

    @property
    def audio_info(self) -> Dict[str, Any]:
        """return audio file information with lazy loading"""
        if self._audio_info is None:
            self._audio_info = self._get_audio_info()
        return self._audio_info

    @property
    def duration(self) -> float:
        """get audio duration in seconds"""
        return self.audio_info["duration"]

    @property
    def formatted_duration(self) -> str:
        """get audio duration as formatted timestamp"""
        return fmt(self.duration)

    def _get_audio_info(self) -> Dict[str, Any]:
        """return duration (seconds) + ffprobe json block for extra hacking"""
        cmd = [
            "ffprobe",
            "-v",
            "error",
            "-show_entries",
            "format=duration:format_tags:stream_tags",
            "-of",
            "json",
            str(self.file_path),
        ]
        try:
            res = subprocess.run(cmd, text=True, capture_output=True, check=True)
        except subprocess.CalledProcessError as exc:
            self.log.error("ffprobe failed: {}", exc.stderr.strip())
            raise RuntimeError(f"ffprobe failed: {exc.stderr.strip()}")

        info = json.loads(res.stdout)
        duration = float(info["format"]["duration"])
        return {"duration": duration, "ffprobe": info}

    def detect(self) -> List[Range]:
        """detect silence spans using configured method"""
        if self._spans is not None:
            return self._spans

        self.log.info("duration: {} ({:.2f} s)", self.formatted_duration, self.duration)

        if self.method == DetectionMethod.PYDUB:
            self._spans = self._detect_pydub()
        else:
            self._spans = self._detect_ffmpeg()

        return self._spans

    def _detect_pydub(self) -> List[Range]:
        """detect silence ranges using pydub's amplitude‑based method"""
        audio = AudioSegment.from_file(self.file_path)

        # optionally downsample for faster processing
        if self.downsample_rate and self.downsample_rate < audio.frame_rate:
            self.log.info(
                "downsampling from {} hz to {} hz for faster processing",
                audio.frame_rate,
                self.downsample_rate,
            )
            # downsample by converting frame rate
            audio = audio.set_frame_rate(self.downsample_rate)

        # determine threshold once for whole file to be consistent
        if self.rel_thresh is not None:
            thresh = audio.dBFS - self.rel_thresh
            self.log.debug(
                "relative threshold: avg {:.1f} dbfs – {:.1f} db = {:.1f} dbfs",
                audio.dBFS,
                self.rel_thresh,
                thresh,
            )
        elif self.abs_thresh is not None:
            thresh = self.abs_thresh
            self.log.debug("absolute threshold: {:.1f} dbfs", thresh)
        else:
            raise ValueError("one of rel_thresh or abs_thresh must be provided")

        spans = detect_silence(audio, min_silence_len=self.min_silence_len, silence_thresh=thresh)
        self.log.info("detected {} silence span(s)", len(spans))
        return [Range(s / _MILLIS, e / _MILLIS) for s, e in spans]

    def _detect_ffmpeg(self) -> List[Range]:
        """detect silence by streaming ffmpeg's *silencedetect* filter"""
        # determine threshold
        if self.rel_thresh is not None:
            # need average loudness first (1‑pass). we approximate via pydub
            audio = AudioSegment.from_file(self.file_path)
            abs_thresh = audio.dBFS - self.rel_thresh
            self.log.debug("ffmpeg relative threshold → {:.1f} dbfs", abs_thresh)
        elif self.abs_thresh is not None:
            abs_thresh = self.abs_thresh
        else:
            abs_thresh = -30.0  # sensible default

        noise = f"{abs_thresh}dB"
        duration = self.min_silence_len / 1000.0

        cmd = [
            "ffmpeg",
            "-hide_banner",
            "-i",
            str(self.file_path),
            "-af",
            f"silencedetect=noise={noise}:d={duration}",
            "-f",
            "null",
            "-",
        ]
        proc = subprocess.Popen(cmd, stderr=subprocess.PIPE, text=True)
        spans: List[Range] = []
        start: Optional[float] = None
        pat_start = re.compile(r"silence_start: (\d+\.?\d*)")
        pat_end = re.compile(r"silence_end: (\d+\.?\d*)")

        assert proc.stderr is not None  # for mypy
        for line in proc.stderr:
            if m := pat_start.search(line):
                start = float(m.group(1))
            elif m := pat_end.search(line):
                end = float(m.group(1))
                if start is None:
                    # ffmpeg prints end even if start was <0 (e.g., file begins in silence)
                    start = 0.0
                spans.append(Range(start, end))
                start = None
        proc.wait()

        if proc.returncode != 0:
            self.log.error("ffmpeg exited with {}", proc.returncode)
            raise RuntimeError(f"ffmpeg exited with {proc.returncode}")

        self.log.info("detected {} silence span(s)", len(spans))
        return spans

    def get_cut_points(self) -> List[float]:
        """get list of cut points (silence starts) sorted chronologically"""
        spans = self.detect()
        cut_points = [r.start for r in spans if r.start > 0]
        cut_points.sort()
        return cut_points

    def export_json(
        self, output_path: Optional[Union[str, Path]] = None, padding_ms: int = 0
    ) -> None:
        """export labeled silence blocks to json file or stdout

        args:
            output_path: path to save json output, if None prints to stdout
            padding_ms: milliseconds to add as padding to silence blocks
        """
        # get silence blocks in the labeled format by default
        blocks = self.get_labeled_silence_blocks(padding_ms=padding_ms)

        if output_path:
            Path(output_path).write_text(json.dumps(blocks, indent=2))
            self.log.info("labeled silence blocks saved to {}", output_path)
        else:
            self.log.success(json.dumps(blocks, indent=2))

    def export_points(self, output_path: Optional[Union[str, Path]] = None) -> None:
        """export just cut points to json file or stdout (original behavior)"""
        cut_points = self.get_cut_points()

        if output_path:
            Path(output_path).write_text(json.dumps(cut_points, indent=2))
            self.log.info("json cut points saved to {}", output_path)
        else:
            self.log.success(json.dumps(cut_points, indent=2))

    def export_csv(self, output_path: Optional[Union[str, Path]] = None) -> None:
        """export cut points to csv file or stdout"""
        cut_points = self.get_cut_points()

        if output_path:
            with open(output_path, "w", newline="") as fh:
                writer = csv.writer(fh)
                writer.writerows([[cp] for cp in cut_points])
            self.log.info("csv saved to {}", output_path)
        else:
            writer = csv.writer(sys.stdout)
            writer.writerows([[cp] for cp in cut_points])

    def cut_media_at_silence_points(
        self, output_dir: Optional[Union[str, Path]] = None
    ) -> List[str]:
        """cut media file into segments at silence points

        args:
            output_dir: directory to save segments (defaults to data/output/{uuid})

        returns:
            list of output file paths
        """
        cut_points = self.get_cut_points()

        # create unique output directory if not specified
        if output_dir is None:
            unique_id = str(uuid.uuid4())[:8]  # use first 8 chars of uuid
            output_dir = Path(_OUTPUT_DIR) / unique_id
        else:
            output_dir = Path(output_dir)

        # ensure output directory exists
        output_dir.mkdir(parents=True, exist_ok=True)
        self.log.info("cutting media into segments at {} cut points", len(cut_points))
        self.log.info("output directory: {}", output_dir)

        # add file end as final cut point
        total_duration = self.duration
        all_points = [0.0] + cut_points + [total_duration]

        output_files = []

        # cut file at each point
        for i in range(len(all_points) - 1):
            start_time = all_points[i]
            end_time = all_points[i + 1]
            duration = end_time - start_time

            # skip segments shorter than 0.1 seconds
            if duration < 0.1:
                self.log.warning(
                    "skipping very short segment {}: {:.2f}-{:.2f} seconds", i, start_time, end_time
                )
                continue

            output_file = output_dir / f"segment_{i:03d}.mp3"

            # ffmpeg command to extract segment
            cmd = [
                "ffmpeg",
                "-y",  # overwrite output files without asking
                "-ss",
                f"{start_time:.3f}",  # start time
                "-i",
                str(self.file_path),
                "-t",
                f"{duration:.3f}",  # duration
                "-vn",  # disable video if source is video
                "-acodec",
                "libmp3lame",  # use mp3 encoding
                "-ab",
                "192k",  # bitrate
                str(output_file),
            ]

            self.log.debug(
                "extracting segment {}: {:.2f}-{:.2f} sec (duration: {:.2f}s)",
                i,
                start_time,
                end_time,
                duration,
            )

            try:
                subprocess.run(cmd, check=True, capture_output=True, text=True)
                self.log.info("saved segment {} to {}", i, output_file)
                output_files.append(str(output_file))
            except subprocess.CalledProcessError as e:
                self.log.error("failed to extract segment {}: {}", i, str(e))
                if e.stderr:
                    self.log.error("ffmpeg error output: {}", e.stderr)

        self.log.success("cutting complete - created {} segments", len(output_files))
        return output_files

    def get_silence_ranges(self) -> List[Range]:
        """return validated, merged silence ranges (start-, end-time in seconds)."""
        spans = sorted(self.detect(), key=lambda r: r.start)

        # merge overlaps / adjacents so every range is unique and valid
        merged: List[Range] = []
        for r in spans:
            if not merged or r.start > merged[-1].end:
                merged.append(Range(r.start, r.end))
            else:  # overlap or touch → extend
                merged[-1].end = max(merged[-1].end, r.end)

        # clip to media duration and drop anomalies
        dur = self.duration
        merged = [
            Range(max(0.0, r.start), min(r.end, dur))
            for r in merged
            if r.end > r.start and r.start < dur
        ]

        return merged

    def export_silence_blocks(
        self,
        padding_ms: int = 0,
        output_path: Optional[Union[str, Path]] = None,
        silent: bool = False,
    ) -> List[List[float]]:
        """export silence blocks with optional padding in [start,end] format

        args:
            padding_ms: milliseconds to add as padding to silence blocks
            output_path: path to save json output, if None prints to stdout
            silent: when True, suppresses console output (for internal use)

        returns:
            list of [start,end] silence blocks with padding applied
        """
        silence_ranges = self.get_silence_ranges()
        padding_sec = padding_ms / 1000.0  # convert to seconds

        # apply padding to silence blocks
        padded_blocks = []
        for r in silence_ranges:
            # expand block by padding (but don't go below 0 or beyond duration)
            padded_start = max(0.0, r.start - padding_sec)
            padded_end = min(self.duration, r.end + padding_sec)
            padded_blocks.append([padded_start, padded_end])

        if not silent:
            self.log.info(
                "exported {} silence blocks with {}ms padding", len(padded_blocks), padding_ms
            )

            # log calculation details for first few blocks
            for i, block in enumerate(padded_blocks[:3]):
                original = silence_ranges[i]
                self.log.debug(
                    "block {}: original [{:.2f},{:.2f}], with padding [{:.2f},{:.2f}]",
                    i,
                    original.start,
                    original.end,
                    block[0],
                    block[1],
                )

        # export to json if output path provided
        if output_path:
            Path(output_path).write_text(json.dumps(padded_blocks, indent=2))
            self.log.info("silence blocks saved to {}", output_path)
        elif not silent:
            self.log.success(json.dumps(padded_blocks, indent=2))

        return padded_blocks

    def get_labeled_silence_blocks(self, padding_ms: int = 0) -> List[Dict[str, float]]:
        """get silence blocks with 'start' and 'end' labels

        args:
            padding_ms: milliseconds to add as padding to silence blocks

        returns:
            list of {"start": start_time, "end": end_time, "duration": duration_time} for each silence block
        """
        # get raw silence blocks with padding
        blocks = self.export_silence_blocks(padding_ms=padding_ms, output_path=None, silent=True)

        # format as simple dictionaries with start/end keys
        labeled_blocks = []
        for start, end in blocks:
            duration = end - start
            labeled_blocks.append({"start": start, "end": end, "duration": duration})

        self.log.debug("formatted {} silence blocks with labels", len(labeled_blocks))
        return labeled_blocks

    def get_speech_segments(self, padding_ms: int = 300) -> List[tuple[float, float]]:
        """return non-silent segments you actually want to keep.

        args:
            padding_ms: milliseconds to add as padding around speech (default: 300ms)
        """
        silences = self.get_silence_ranges()
        keep: List[tuple[float, float]] = []
        padding_sec = padding_ms / 1000.0  # convert to seconds

        cur = 0.0
        for s in silences:
            if s.start > cur:
                # add speech segment with padding
                # start with padding but not before 0
                speech_start = max(0.0, cur - padding_sec)
                # end with padding but not into next silence
                speech_end = min(s.start + padding_sec, s.end)
                keep.append((speech_start, speech_end))
            cur = max(cur, s.end)

        # handle tail after last silence with padding
        if cur < self.duration:
            keep.append((max(0.0, cur - padding_sec), self.duration))

        # merge segments that overlap due to padding
        merged: List[tuple[float, float]] = []
        for start, end in sorted(keep):
            if not merged or start > merged[-1][1]:
                merged.append((start, end))
            else:  # overlap due to padding - merge them
                merged[-1] = (merged[-1][0], max(merged[-1][1], end))

        # drop ridiculously small chunks (<100 ms)
        return [(a, b) for a, b in merged if b - a >= 0.1]

    def cut_media_skipping_silence(
        self,
        output_dir: Union[str, Path, None] = None,
        ext: str = ".mp3",
        padding_ms: int = 400,
    ) -> List[str]:
        """export *non-silent* parts as consecutive files.

        args:
            output_dir: directory to save segments
            ext: file extension for output files
            padding_ms: milliseconds to add as padding around speech (default: 400ms)

        returns:
            list of output file paths
        """

        keep_segments = self.get_speech_segments(padding_ms=padding_ms)
        if output_dir is None:
            output_dir = Path(_OUTPUT_DIR) / f"{uuid.uuid4().hex[:8]}"
        else:
            output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        self.log.info(
            "saving {} speech segments with {}ms padding to {}",
            len(keep_segments),
            padding_ms,
            output_dir,
        )

        out_paths: List[str] = []
        for idx, (start, end) in enumerate(keep_segments):
            out_file = output_dir / f"speech_{idx:03d}{ext}"

            # choose encoding parameters based on output format
            if ext.lower() == ".mp3":
                # for mp3 output, we need to encode
                codec_params = ["-c:a", "libmp3lame", "-q:a", "2"]
            elif ext.lower() == ".wav":
                # for wav output, copy is fine
                codec_params = ["-c:a", "pcm_s16le"]
            else:
                # for other formats, let ffmpeg choose appropriate encoder
                codec_params = ["-c:a", "libmp3lame" if ext.lower() == ".mp3" else "aac"]

            cmd = [
                "ffmpeg",
                "-y",
                "-ss",
                f"{start:.3f}",
                "-i",
                str(self.file_path),
                "-t",
                f"{end - start:.3f}",
                *codec_params,  # use appropriate codec instead of just copy
                str(out_file),
            ]
            try:
                subprocess.run(cmd, check=True, capture_output=True, text=True)
                self.log.success(
                    "segment {:03d}: {:.2f}s → {:.2f}s  ➜  {}", idx, start, end, out_file
                )
                out_paths.append(str(out_file))
            except subprocess.CalledProcessError as err:
                self.log.error("ffmpeg failed on segment {:03d}: {}", idx, err.stderr)

        return out_paths


# ---------------------------------------------------------------------------
# command‑line interface
# ---------------------------------------------------------------------------
def build_arg_parser() -> argparse.ArgumentParser:
    """build command line argument parser"""
    p = argparse.ArgumentParser(
        description="Detect silence gaps in an audio/video file.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    p.add_argument("file", help="audio/video file to analyze")
    p.add_argument(
        "--method",
        choices=("pydub", "ffmpeg"),
        default="pydub",
        help="detection backend",
    )
    p.add_argument("--min-len", type=int, default=1000, help="minimum silence length in ms")

    g = p.add_mutually_exclusive_group()
    g.add_argument(
        "--rel-thresh",
        type=float,
        default=16.0,
        help="dB below the file's average loudness to count as silence",
    )
    g.add_argument("--abs-thresh", type=float, help="absolute dBFS threshold (negative value)")

    p.add_argument("--csv", action="store_true", help="output CSV (start,end)")
    p.add_argument(
        "-o",
        "--output",
        metavar="PATH",
        help="write JSON/CSV to file instead of stdout",
    )
    p.add_argument(
        "--no-cut", action="store_true", help="skip cutting media into files (json output only)"
    )
    p.add_argument(
        "--no-skip-silence",
        action="store_false",
        dest="skip_silence",
        default=True,
        help="don't export only speech segments (keep silences)",
    )
    p.add_argument(
        "--speech-padding-ms",
        type=int,
        default=400,
        help="milliseconds padding around speech when skipping silence",
    )
    p.add_argument(
        "--silence-padding-ms",
        type=int,
        default=400,
        help="milliseconds padding around silence blocks when exporting",
    )
    p.add_argument(
        "--points-only",
        action="store_true",
        help="output just cut points instead of labeled silence blocks",
    )
    p.add_argument(
        "--verbose",
        "-v",
        action="store_const",
        const="DEBUG",
        default="INFO",
        dest="loglevel",
        help="debug logging",
    )
    p.add_argument(
        "--downsample",
        type=int,
        default=16000,
        help="downsample audio to this rate (Hz) for faster processing with pydub method",
    )
    return p


def main(argv: Optional[List[str]] = None) -> None:
    """main entry point for command line use"""
    args = build_arg_parser().parse_args(argv)

    try:
        # create detector with command line args
        detector = SilenceDetector(
            file_path=args.file,
            method=args.method,
            min_silence_len=args.min_len,
            rel_thresh=args.rel_thresh if args.abs_thresh is None else None,
            abs_thresh=args.abs_thresh,
            log_level=args.loglevel,
            downsample_rate=args.downsample,
        )

        # run detection
        detector.detect()

        # cut media if requested
        if not args.no_cut:
            if args.skip_silence:
                detector.cut_media_skipping_silence(padding_ms=args.speech_padding_ms)
            else:
                detector.cut_media_at_silence_points()

        # export results based on format
        if args.csv:
            detector.export_csv(args.output)
        elif args.points_only:
            detector.export_points(args.output)
        else:
            # use the labeled blocks format by default with optional padding
            detector.export_json(args.output, padding_ms=args.silence_padding_ms)

    except Exception as e:
        log.error("error: {}", str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
