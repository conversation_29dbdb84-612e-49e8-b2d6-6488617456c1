# audio separation integration in smart chunker

the smart chunker pipeline now includes comprehensive audio separation functionality using demucs, allowing you to separate audio into vocal and instrumental stems before processing.

## overview

audio separation is integrated as an optional preprocessing step in the smart chunker pipeline. when enabled, it:

1. **separates the input audio** into stems (vocals, drums, bass, other, etc.)
2. **uses the separated vocals** for transcription and further processing (if available)
3. **saves all separated stems** to the output directory for later use
4. **falls back to original audio** if separation fails or vocals aren't available

## configuration

### json configuration

add the `audio_separation` section to your `run_config.json`:

```json
{
    "input_processing": {
        "audio_separation": {
            "enabled": true,
            "model": "htdemucs",
            "stems": "vocals",
            "output_format": "wav",
            "segment_length": 30,
            "device": "auto",
            "mp3_bitrate": 320,
            "overlap": 0.25,
            "shifts": 1,
            "jobs": 1
        }
    }
}
```

### configuration options

- **`enabled`** (boolean): enable/disable audio separation
- **`model`** (string): separation model to use
  - `htdemucs`: default hybrid transformer model (recommended)
  - `htdemucs_ft`: fine-tuned version (better quality, 4x slower)
  - `htdemucs_6s`: 6-source model (adds piano and guitar)
  - `hdemucs_mmi`: hybrid demucs v3 retrained
  - `mdx`: trained on musdb hq only
  - `mdx_extra`: trained with extra data
- **`stems`** (string): which stems to extract
  - `vocals`: vocal tracks only
  - `drums`: drum tracks only
  - `bass`: bass tracks only
  - `other`: other instruments
  - `all`: all available stems
- **`output_format`** (string): output audio format (`wav`, `mp3`, `flac`)
- **`segment_length`** (integer): segment length in seconds for memory management
- **`device`** (string): processing device (`auto`, `cpu`, `cuda`)
- **`mp3_bitrate`** (integer): mp3 bitrate in kbps (when using mp3 format)
- **`overlap`** (float): overlap between prediction windows (0.0-1.0)
- **`shifts`** (integer): number of random shifts for prediction averaging
- **`jobs`** (integer): number of parallel jobs

## command line interface

override configuration settings via command line:

```bash
# enable separation with default settings
python -m smart_chunker.cli.entrypoint --enable-separation

# use fine-tuned model for better quality
python -m smart_chunker.cli.entrypoint --enable-separation --separation-model htdemucs_ft

# extract only vocals with cpu processing
python -m smart_chunker.cli.entrypoint --enable-separation --separation-stems vocals --separation-device cpu

# use memory management for large files
python -m smart_chunker.cli.entrypoint --enable-separation --separation-segment-length 30
```

## usage examples

### basic usage with separation enabled

```bash
# using configuration file with separation enabled
python -m smart_chunker.cli.entrypoint --config smart_chunker/config/run_config_with_separation.json

# enabling separation via cli
python -m smart_chunker.cli.entrypoint --enable-separation
```

### programmatic usage

```python
from smart_chunker.audio.separation import AudioSeparator, AudioSeparationConfig, StemType
from smart_chunker.processing.audio_separation import process_audio_separation

# direct audio separation
config = AudioSeparationConfig(stems=StemType.VOCALS)
separator = AudioSeparator(config)
result = separator.separate_audio("input.mp3")

# integration with pipeline
separation_result = process_audio_separation(
    "input.mp3", 
    pipeline_config, 
    "run_id_123", 
    "output/directory"
)
```

## output structure

when audio separation is enabled, the output structure includes:

```
data/output/
├── run_id/
│   ├── audio_separation/
│   │   └── htdemucs/
│   │       └── track_name/
│   │           ├── vocals.wav
│   │           ├── drums.wav
│   │           ├── bass.wav
│   │           └── other.wav
│   ├── segments/
│   ├── transcripts/
│   └── translations/
```

## memory requirements

- **minimum**: 3gb gpu memory for basic processing
- **recommended**: 8gb+ gpu memory for optimal performance
- **cpu fallback**: available with `--separation-device cpu`
- **memory management**: use `segment_length` parameter for large files

## performance considerations

1. **model selection**:
   - `htdemucs`: balanced quality and speed (default)
   - `htdemucs_ft`: best quality but 4x slower
   - `mdx_q`: fastest but lower quality

2. **device selection**:
   - `cuda`: fastest with gpu
   - `cpu`: slower but works without gpu
   - `auto`: automatically selects best available

3. **memory optimization**:
   - use `segment_length` for large files
   - reduce `jobs` if running out of memory
   - consider `mdx_q` model for lower memory usage

## integration with pipeline

the audio separation integrates seamlessly with the existing pipeline:

1. **preprocessing**: separation runs before segmentation
2. **vocal extraction**: separated vocals are used for transcription
3. **fallback**: original audio used if separation fails
4. **logging**: comprehensive logging of separation process
5. **validation**: configuration validation before processing

## troubleshooting

### common issues

1. **out of memory errors**:
   - reduce `segment_length` (try 10-30 seconds)
   - use `--separation-device cpu`
   - reduce `jobs` parameter

2. **slow processing**:
   - use `htdemucs` instead of `htdemucs_ft`
   - increase `segment_length` if memory allows
   - ensure cuda is available and working

3. **poor separation quality**:
   - try `htdemucs_ft` model for better quality
   - increase `shifts` parameter (slower but better)
   - ensure input audio quality is good

### debugging

enable debug logging to see detailed separation process:

```json
{
    "logging": {
        "level": "DEBUG"
    }
}
```

## examples

see the example configuration files:
- `smart_chunker/config/run_config.json` (separation disabled)
- `smart_chunker/config/run_config_with_separation.json` (separation enabled)

the audio separation functionality is now fully integrated into the smart chunker pipeline, providing high-quality stem separation while maintaining the existing workflow and adding powerful new capabilities for audio processing.
