"""smart chunker pipeline abstraction.

this module introduces :class:`smartchunkerpipeline` which will ultimately own
all high-level workflow logic. for now it proxies to the legacy `run.main()`
function to allow incremental refactor without breaking behaviour.
"""

from __future__ import annotations

from loguru import logger

from smart_chunker.core.config_manager import load_config, runconfig


class smartchunkerpipeline:  # pylint: disable=too-few-public-methods
    """orchestrate the smart chunker workflow."""

    def __init__(self, config_path: str):
        self.config: runconfig = load_config(config_path)
        self.run_id: str | None = None

    # ---------------------------------------------------------------------
    # public api
    # ---------------------------------------------------------------------
    def run(self) -> None:  # noqa: d401
        """execute end-to-end processing using modular helpers."""

        import os
        import uuid

        from smart_chunker.core.file_ops import ensure_output_directory, save_translated_chunks_to_file
        from smart_chunker.processing.segments import segment_audio_and_extract_timestamps
        from smart_chunker.processing.transcript import (
            concatenate_transcript,
            segment_transcript,
            map_timestamps_and_export_srt,
        )
        from smart_chunker.processing.translation import (
            analyze_translation_request,
            translate_transcript_chunks,
            save_translated_srt,
        )

        cfg = self.config.raw

        run_id = str(uuid.uuid4())
        logger.info("starting processing pipeline with run id: {}", run_id)

        # --- configuration extraction ------------------------------------------------------
        input_cfg = cfg.get("input_processing", {})
        lang_cfg = cfg.get("translation", {})
        perf_cfg = cfg.get("performance", {})
        output_cfg = cfg.get("output", {})
        models_cfg = cfg.get("models", {})

        original_file = input_cfg.get("original_file", "default_input.mp3")
        input_base = os.path.splitext(os.path.basename(original_file))[0]

        max_parallel_workers = perf_cfg.get("max_parallel_workers", 5)
        rate_limit_rpm = perf_cfg.get("rate_limit_rpm", 150)

        base_output_directory = output_cfg.get("base_directory", "data/output")

        default_model = "gemini-2.5-pro"
        default_temp = 0.1

        audio_timestamp_cfg = models_cfg.get("audio", {}).get("timestamp_generator", {})
        audio_timestamp_model = audio_timestamp_cfg.get("model", default_model)
        audio_timestamp_temp = audio_timestamp_cfg.get("temperature", default_temp)

        audio_processor_cfg = models_cfg.get("audio", {}).get("parallel_processor", {})
        audio_processor_model = audio_processor_cfg.get("model", default_model)
        audio_processor_temp = audio_processor_cfg.get("temperature", 0.4)

        transcription_cfg = models_cfg.get("transcription", {}).get("chunker", {})
        transcription_model = transcription_cfg.get("model", default_model)
        transcription_temp = transcription_cfg.get("temperature", default_temp)

        translation_analyzer_cfg = models_cfg.get("translation", {}).get("analyzer", {})
        translation_analyzer_model = translation_analyzer_cfg.get("model", default_model)
        translation_analyzer_temp = translation_analyzer_cfg.get("temperature", default_temp)

        translation_segment_cfg = models_cfg.get("translation", {}).get("segment", {})
        translation_segment_model = translation_segment_cfg.get("model", default_model)
        translation_segment_temp = translation_segment_cfg.get("temperature", default_temp)

        # -----------------------------------------------------------------------------------

        ensure_output_directory(base_output_directory, run_id)

        if not os.path.exists(original_file):
            logger.error("input file not found: {}. exiting.", original_file)
            return

        temp_dir_obj = None

        try:
            seg_meta, adjusted_ts, temp_dir_obj = segment_audio_and_extract_timestamps(
                original_file,
                cfg,
                run_id,
                max_parallel_workers,
                rate_limit_rpm,
                audio_timestamp_model,
                audio_timestamp_temp,
                audio_processor_model,
                audio_processor_temp,
            )

            if not seg_meta or not adjusted_ts:
                logger.error("no timestamps generated; aborting pipeline")
                return

            full_transcript = concatenate_transcript(adjusted_ts)

            chunks_output, _ = segment_transcript(
                full_transcript,
                input_base,
                base_output_directory,
                run_id,
                model=transcription_model,
                temperature=transcription_temp,
            )

            if not chunks_output:
                return

            mapping_result = map_timestamps_and_export_srt(
                adjusted_ts,
                chunks_output,
                input_base,
                base_output_directory,
                run_id,
            )

            if not mapping_result:
                return

            src_lang = lang_cfg.get("source_language", "uz")
            tgt_lang = lang_cfg.get("target_language", "en")

            style_req = analyze_translation_request(
                full_transcript,
                src_lang,
                tgt_lang,
                model=translation_analyzer_model,
                temperature=translation_analyzer_temp,
            )

            translated_chunks = translate_transcript_chunks(
                chunks_output,
                full_transcript,
                src_lang,
                tgt_lang,
                style_req,
                max_workers=max_parallel_workers,
                rate_limit_rpm=rate_limit_rpm,
                model=translation_segment_model,
                temperature=translation_segment_temp,
            )

            save_translated_chunks_to_file(
                translated_chunks,
                input_base,
                base_output_directory,
                run_id,
                tgt_lang,
            )

            save_translated_srt(
                mapping_result,
                translated_chunks,
                input_base,
                base_output_directory,
                run_id,
                tgt_lang,
            )

            logger.info(
                "processing pipeline completed. output at {}/{}", base_output_directory, run_id
            )

        finally:
            if temp_dir_obj:
                logger.info("cleaning temporary segment directory")
                temp_dir_obj.cleanup()
