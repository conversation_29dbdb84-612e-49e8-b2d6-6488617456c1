{"input_processing": {"original_file": "data/input/forest.mp3", "min_segment_duration_seconds": 60, "use_temporary_directory_for_segments": true, "silence_detection": {"method": "pydub", "min_silence_len_ms": 500, "abs_threshold_db": -30.0, "speech_segment_padding_ms": 200, "max_silence_to_bridge_seconds": 2.0}, "audio_separation": {"enabled": false, "model": "htdemucs", "stems": "all", "output_format": "wav", "segment_length": null, "device": "auto", "mp3_bitrate": 320, "overlap": 0.25, "shifts": 1, "jobs": 1}}, "translation": {"source_language": "uz", "target_language": "en"}, "performance": {"max_parallel_workers": 150, "rate_limit_rpm": 150}, "logging": {"level": "DEBUG"}, "output": {"base_directory": "data/output"}, "models": {"translation": {"analyzer": {"model": "gemini-2.5-pro", "temperature": 0.1}, "segment": {"model": "gemini-2.5-pro", "temperature": 0.1}}, "transcription": {"chunker": {"model": "gemini-2.5-pro", "temperature": 0.1}}, "audio": {"timestamp_generator": {"model": "gemini-2.5-pro", "temperature": 0.1}, "parallel_processor": {"model": "gemini-2.5-pro", "temperature": 0.1}}}}