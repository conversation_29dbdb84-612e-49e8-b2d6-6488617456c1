"""
audio_separation.py - script to separate audio into vocal and instrumental stems

this script uses demucs to separate audio tracks into vocal and other components
"""

import shlex
from pathlib import Path

from demucs import separate

from smart_chunker.utils.logging import setup_logger

# configure logger using the standardized setup
logs_path = Path("smart_chunker/logs")
logs_path.mkdir(parents=True, exist_ok=True)

# remove default handler and use our standardized logger
logger = setup_logger()


def separate_audio(input_file, output_dir=None, stems="vocals", model="htdemucs_ft"):
    """separate audio into stems using demucs.

    args:
        input_file: path to input audio/video file
        output_dir: directory for output (defaults to data/output/htdemucs)
        stems: which stems to extract ('vocals', 'drums', 'bass', 'other', or 'all')
        model: which demucs model to use

    returns:
        path to output directory
    """
    # validate input file exists
    input_path = Path(input_file)
    if not input_path.exists():
        logger.error("input file not found: {}", input_file)
        return None

    # setup output directory
    if output_dir is None:
        output_dir = Path("data/output/htdemucs")
    else:
        output_dir = Path(output_dir)

    output_dir.mkdir(parents=True, exist_ok=True)

    # determine stem argument
    if stems == "all":
        stem_arg = ""  # empty for all stems
    else:
        stem_arg = f"--two-stems {stems}"

    # optional: change how files are named
    name_pattern = "{track}_{stem}.{ext}"

    # build command
    cmd = (
        f"{stem_arg} "
        f"-n {model} "
        f'-o "{output_dir}" '
        f'--filename "{name_pattern}" '
        f"--mp3 "
        f'"{input_path}"'
        f" -j 8"  # use 8 threads for processing
    )

    logger.debug("running demucs with command: {}", cmd)
    separate.main(shlex.split(cmd))
    logger.info("saved stems in {}/", output_dir)

    return output_dir


if __name__ == "__main__":
    # example usage
    input_file = "data/input/gor_odami_5min_video.mp4"
    separate_audio(input_file)
