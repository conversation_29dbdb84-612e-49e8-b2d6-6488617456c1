"""
audio_separation.py - comprehensive audio separation pipeline

this script provides a complete audio separation pipeline with support for multiple
models, formats, and configuration options. it includes proper error handling,
logging, and a command-line interface for production use.
"""

import argparse
import json
import shlex
import sys
import traceback
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Union

import torch
import torchaudio
from demucs import separate
from pydub import AudioSegment

from smart_chunker.utils.logging import setup_logger

# configure logger using the standardized setup
logger = setup_logger()


class SeparationModel(Enum):
    """available separation models with their characteristics."""

    HTDEMUCS = "htdemucs"  # default hybrid transformer model
    HTDEMUCS_FT = "htdemucs_ft"  # fine-tuned version (4x slower but better)
    HTDEMUCS_6S = "htdemucs_6s"  # 6 sources (adds piano and guitar)
    HDEMUCS_MMI = "hdemucs_mmi"  # hybrid demucs v3 retrained
    MDX = "mdx"  # trained on musdb hq only
    MDX_EXTRA = "mdx_extra"  # trained with extra data
    MDX_Q = "mdx_q"  # quantized mdx (smaller, slightly worse quality)
    MDX_EXTRA_Q = "mdx_extra_q"  # quantized mdx_extra


class OutputFormat(Enum):
    """supported output audio formats."""

    WAV = "wav"
    MP3 = "mp3"
    FLAC = "flac"


class StemType(Enum):
    """available stem types for separation."""

    VOCALS = "vocals"
    DRUMS = "drums"
    BASS = "bass"
    OTHER = "other"
    PIANO = "piano"  # only available with htdemucs_6s
    GUITAR = "guitar"  # only available with htdemucs_6s
    ALL = "all"


class AudioSeparationConfig:
    """configuration class for audio separation pipeline."""

    def __init__(
        self,
        model: SeparationModel = SeparationModel.HTDEMUCS,
        output_format: OutputFormat = OutputFormat.WAV,
        stems: StemType = StemType.ALL,
        output_dir: Optional[Path] = None,
        segment_length: Optional[int] = None,
        overlap: float = 0.25,
        shifts: int = 1,
        jobs: int = 1,
        device: str = "auto",
        mp3_bitrate: int = 320,
        mp3_preset: int = 2,
        float32: bool = False,
        int24: bool = False,
        clip_mode: str = "rescale",
        filename_pattern: str = "{track}_{stem}.{ext}",
    ):
        """initialize audio separation configuration.

        args:
            model: separation model to use
            output_format: output audio format
            stems: which stems to extract
            output_dir: output directory (defaults to data/output/audio_separation)
            segment_length: segment length in seconds for memory management
            overlap: overlap between prediction windows (0.0-1.0)
            shifts: number of random shifts for prediction averaging
            jobs: number of parallel jobs
            device: device to use ('auto', 'cpu', 'cuda')
            mp3_bitrate: mp3 bitrate in kbps
            mp3_preset: mp3 encoder preset (2=best quality, 7=fastest)
            float32: save as float32 wav files
            int24: save as 24-bit integer wav files
            clip_mode: clipping mode ('rescale' or 'clamp')
            filename_pattern: output filename pattern
        """
        self.model = model
        self.output_format = output_format
        self.stems = stems
        self.output_dir = output_dir or Path("data/output/audio_separation")
        self.segment_length = segment_length
        self.overlap = overlap
        self.shifts = shifts
        self.jobs = jobs
        self.device = device
        self.mp3_bitrate = mp3_bitrate
        self.mp3_preset = mp3_preset
        self.float32 = float32
        self.int24 = int24
        self.clip_mode = clip_mode
        self.filename_pattern = filename_pattern

    def to_dict(self) -> Dict:
        """convert configuration to dictionary."""
        return {
            "model": self.model.value,
            "output_format": self.output_format.value,
            "stems": self.stems.value,
            "output_dir": str(self.output_dir),
            "segment_length": self.segment_length,
            "overlap": self.overlap,
            "shifts": self.shifts,
            "jobs": self.jobs,
            "device": self.device,
            "mp3_bitrate": self.mp3_bitrate,
            "mp3_preset": self.mp3_preset,
            "float32": self.float32,
            "int24": self.int24,
            "clip_mode": self.clip_mode,
            "filename_pattern": self.filename_pattern,
        }

    @classmethod
    def from_dict(cls, config_dict: Dict) -> "AudioSeparationConfig":
        """create configuration from dictionary."""
        return cls(
            model=SeparationModel(config_dict.get("model", "htdemucs")),
            output_format=OutputFormat(config_dict.get("output_format", "wav")),
            stems=StemType(config_dict.get("stems", "all")),
            output_dir=Path(config_dict.get("output_dir", "data/output/audio_separation")),
            segment_length=config_dict.get("segment_length"),
            overlap=config_dict.get("overlap", 0.25),
            shifts=config_dict.get("shifts", 1),
            jobs=config_dict.get("jobs", 1),
            device=config_dict.get("device", "auto"),
            mp3_bitrate=config_dict.get("mp3_bitrate", 320),
            mp3_preset=config_dict.get("mp3_preset", 2),
            float32=config_dict.get("float32", False),
            int24=config_dict.get("int24", False),
            clip_mode=config_dict.get("clip_mode", "rescale"),
            filename_pattern=config_dict.get("filename_pattern", "{track}_{stem}.{ext}"),
        )
