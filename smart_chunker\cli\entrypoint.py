"""command-line entrypoint for smart chunker.

usage::

    python -m smart_chunker.cli.entrypoint --config smart_chunker/config/run_config.json
"""

from __future__ import annotations

import argparse
import sys
from pathlib import Path

from loguru import logger

from smart_chunker.pipeline.main import smartchunkerpipeline


def _build_parser() -> argparse.ArgumentParser:  # noqa: d401
    parser = argparse.ArgumentParser(description="smart chunker processing pipeline")
    parser.add_argument(
        "--config",
        type=Path,
        default=Path("smart_chunker/config/run_config.json"),
        help="path to the run configuration json file",
    )

    # audio separation options
    separation_group = parser.add_argument_group("audio separation options")
    separation_group.add_argument(
        "--enable-separation",
        action="store_true",
        help="enable audio separation (overrides config file setting)",
    )
    separation_group.add_argument(
        "--separation-model",
        type=str,
        choices=["htdemucs", "htdemucs_ft", "htdemucs_6s", "hdemucs_mmi", "mdx", "mdx_extra"],
        help="audio separation model to use",
    )
    separation_group.add_argument(
        "--separation-stems",
        type=str,
        choices=["vocals", "drums", "bass", "other", "all"],
        help="which stems to extract",
    )
    separation_group.add_argument(
        "--separation-device",
        type=str,
        choices=["auto", "cpu", "cuda"],
        help="device to use for audio separation",
    )
    separation_group.add_argument(
        "--separation-segment-length",
        type=int,
        help="segment length in seconds for memory management",
    )

    return parser


def _apply_separation_overrides(args: argparse.Namespace, pipeline) -> None:
    """apply cli overrides for audio separation configuration."""
    # check if any separation arguments were provided
    has_separation_args = any([
        args.enable_separation,
        args.separation_model,
        args.separation_stems,
        args.separation_device,
        args.separation_segment_length,
    ])

    if not has_separation_args:
        return

    logger.info("applying audio separation cli overrides")

    # get the current config
    config = pipeline.config.raw

    # ensure audio_separation section exists
    if "input_processing" not in config:
        config["input_processing"] = {}
    if "audio_separation" not in config["input_processing"]:
        config["input_processing"]["audio_separation"] = {}

    separation_config = config["input_processing"]["audio_separation"]

    # apply overrides
    if args.enable_separation:
        separation_config["enabled"] = True
        logger.info("enabled audio separation via cli")

    if args.separation_model:
        separation_config["model"] = args.separation_model
        logger.info("set separation model to: {}", args.separation_model)

    if args.separation_stems:
        separation_config["stems"] = args.separation_stems
        logger.info("set separation stems to: {}", args.separation_stems)

    if args.separation_device:
        separation_config["device"] = args.separation_device
        logger.info("set separation device to: {}", args.separation_device)

    if args.separation_segment_length:
        separation_config["segment_length"] = args.separation_segment_length
        logger.info("set separation segment length to: {}", args.separation_segment_length)


def main(argv: list[str] | None = None) -> None:  # noqa: d401
    """parse arguments and kick off the pipeline."""

    argv = argv if argv is not None else sys.argv[1:]
    parser = _build_parser()
    args = parser.parse_args(argv)

    pipeline = smartchunkerpipeline(str(args.config))

    # apply cli overrides for audio separation
    _apply_separation_overrides(args, pipeline)

    try:
        pipeline.run()
    except KeyboardInterrupt:  # pragma: no cover
        logger.warning("interrupted by user (ctrl-c). exiting immediately.")
        sys.exit(1)


if __name__ == "__main__":
    main()
