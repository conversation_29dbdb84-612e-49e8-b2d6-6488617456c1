"""
audio separation processing module for smart chunker pipeline

this module provides integration between the audio separation functionality
and the main smart chunker processing pipeline.
"""

from __future__ import annotations

import os
from pathlib import Path
from typing import Dict, Optional

from loguru import logger

from smart_chunker.audio.separation import (
    AudioSeparationConfig,
    AudioSeparator,
    OutputFormat,
    SeparationModel,
    StemType,
)


def process_audio_separation(
    input_file: str,
    config: Dict,
    run_id: str,
    base_output_directory: str,
) -> Optional[Dict]:
    """process audio separation as part of the smart chunker pipeline.
    
    args:
        input_file: path to the input audio/video file
        config: configuration dictionary from run_config.json
        run_id: unique run identifier
        base_output_directory: base output directory for the pipeline
        
    returns:
        dictionary with separation results, or none if separation is disabled
    """
    # check if audio separation is enabled
    separation_config = config.get("input_processing", {}).get("audio_separation", {})
    
    if not separation_config.get("enabled", False):
        logger.info("audio separation is disabled, skipping")
        return None
    
    logger.info("starting audio separation processing")
    
    try:
        # setup output directory for separation
        separation_output_dir = Path(base_output_directory) / run_id / "audio_separation"
        
        # create audio separation configuration
        audio_config = AudioSeparationConfig(
            model=SeparationModel(separation_config.get("model", "htdemucs")),
            output_format=OutputFormat(separation_config.get("output_format", "wav")),
            stems=StemType(separation_config.get("stems", "all")),
            output_dir=separation_output_dir,
            segment_length=separation_config.get("segment_length"),
            overlap=separation_config.get("overlap", 0.25),
            shifts=separation_config.get("shifts", 1),
            jobs=separation_config.get("jobs", 1),
            device=separation_config.get("device", "auto"),
            mp3_bitrate=separation_config.get("mp3_bitrate", 320),
        )
        
        # initialize separator
        separator = AudioSeparator(audio_config)
        
        # perform separation
        result = separator.separate_audio(input_file)
        
        # log results
        logger.info("audio separation completed successfully")
        logger.info("separated stems: {}", list(result["output_files"].keys()))
        
        # add run_id to result for tracking
        result["run_id"] = run_id
        
        return result
        
    except Exception as e:
        logger.error("audio separation failed: {}", str(e))
        logger.debug("full error details", exc_info=True)
        # don't raise - separation failure shouldn't stop the main pipeline
        return None


def get_separated_audio_file(
    separation_result: Optional[Dict],
    stem_type: str = "vocals",
    fallback_to_original: bool = True,
    original_file: Optional[str] = None,
) -> str:
    """get a specific separated audio file path.
    
    args:
        separation_result: result from process_audio_separation
        stem_type: type of stem to retrieve ('vocals', 'drums', 'bass', 'other')
        fallback_to_original: whether to fallback to original file if stem not found
        original_file: original file path for fallback
        
    returns:
        path to the requested stem file, or original file if not available
    """
    if not separation_result or "output_files" not in separation_result:
        if fallback_to_original and original_file:
            logger.info("no separation result available, using original file")
            return original_file
        else:
            raise ValueError("no separation result available and no fallback specified")
    
    output_files = separation_result["output_files"]
    
    if stem_type in output_files:
        stem_path = str(output_files[stem_type])
        logger.info("using separated {} stem: {}", stem_type, stem_path)
        return stem_path
    
    # try to find any available stem if requested type not found
    available_stems = list(output_files.keys())
    if available_stems:
        fallback_stem = available_stems[0]
        stem_path = str(output_files[fallback_stem])
        logger.warning(
            "requested stem '{}' not found, using '{}' instead: {}",
            stem_type, fallback_stem, stem_path
        )
        return stem_path
    
    # no stems available
    if fallback_to_original and original_file:
        logger.warning("no separated stems available, falling back to original file")
        return original_file
    else:
        raise ValueError(f"stem '{stem_type}' not found in separation results")


def create_separation_summary(separation_result: Optional[Dict]) -> Dict:
    """create a summary of the audio separation results.
    
    args:
        separation_result: result from process_audio_separation
        
    returns:
        summary dictionary with separation information
    """
    if not separation_result:
        return {
            "enabled": False,
            "status": "disabled",
            "stems_created": [],
            "output_directory": None,
            "model_used": None,
        }
    
    output_files = separation_result.get("output_files", {})
    
    return {
        "enabled": True,
        "status": "completed" if output_files else "failed",
        "stems_created": list(output_files.keys()),
        "output_directory": separation_result.get("output_directory"),
        "model_used": separation_result.get("model_used"),
        "input_file": separation_result.get("input_file"),
        "audio_info": separation_result.get("audio_info", {}),
        "file_paths": {stem: str(path) for stem, path in output_files.items()},
    }


def validate_separation_config(config: Dict) -> bool:
    """validate audio separation configuration.
    
    args:
        config: configuration dictionary
        
    returns:
        true if configuration is valid, false otherwise
    """
    separation_config = config.get("input_processing", {}).get("audio_separation", {})
    
    if not separation_config.get("enabled", False):
        return True  # disabled config is valid
    
    try:
        # validate model
        model = separation_config.get("model", "htdemucs")
        SeparationModel(model)
        
        # validate stems
        stems = separation_config.get("stems", "all")
        StemType(stems)
        
        # validate output format
        output_format = separation_config.get("output_format", "wav")
        OutputFormat(output_format)
        
        # validate numeric values
        overlap = separation_config.get("overlap", 0.25)
        if not 0.0 <= overlap <= 1.0:
            logger.error("invalid overlap value: {}, must be between 0.0 and 1.0", overlap)
            return False
        
        shifts = separation_config.get("shifts", 1)
        if shifts < 1:
            logger.error("invalid shifts value: {}, must be >= 1", shifts)
            return False
        
        jobs = separation_config.get("jobs", 1)
        if jobs < 1:
            logger.error("invalid jobs value: {}, must be >= 1", jobs)
            return False
        
        mp3_bitrate = separation_config.get("mp3_bitrate", 320)
        if mp3_bitrate <= 0:
            logger.error("invalid mp3_bitrate value: {}, must be > 0", mp3_bitrate)
            return False
        
        logger.debug("audio separation configuration validation passed")
        return True
        
    except ValueError as e:
        logger.error("invalid audio separation configuration: {}", str(e))
        return False


def get_separation_config_summary(config: Dict) -> Dict:
    """get a summary of the audio separation configuration.
    
    args:
        config: configuration dictionary
        
    returns:
        summary of separation configuration
    """
    separation_config = config.get("input_processing", {}).get("audio_separation", {})
    
    return {
        "enabled": separation_config.get("enabled", False),
        "model": separation_config.get("model", "htdemucs"),
        "stems": separation_config.get("stems", "all"),
        "output_format": separation_config.get("output_format", "wav"),
        "device": separation_config.get("device", "auto"),
        "segment_length": separation_config.get("segment_length"),
        "overlap": separation_config.get("overlap", 0.25),
        "shifts": separation_config.get("shifts", 1),
        "jobs": separation_config.get("jobs", 1),
        "mp3_bitrate": separation_config.get("mp3_bitrate", 320),
    }
