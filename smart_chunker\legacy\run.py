from __future__ import annotations
from loguru import logger

"""deprecated compatibility shim.

this module remains to satisfy existing import paths. it simply delegates to
the new cli entrypoint which spins up :class:`smartchunkerpipeline`.
"""

CONFIG_FILE_PATH = "smart_chunker/config/run_config.json"  # preserved for legacy users


def main() -> None:  # noqa: d401
    """delegate execution to ``smart_chunker.cli_main``."""

                logger.warning(
        "`smart_chunker.run` is deprecated; please invoke `python -m smart_chunker.cli_main` "
        "or use the `smartchunkerpipeline` api. delegating to cli_main now."
    )

    from smart_chunker.cli_main import main as _cli_main

    _cli_main([])


if __name__ == "__main__":
        main()
